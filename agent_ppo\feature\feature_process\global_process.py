#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2024 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""

from enum import Enum
from agent_ppo.feature.feature_process.feature_normalizer import FeatureNormalizer
import configparser
import os
import math
from collections import OrderedDict


class GlobalProcess:
    """
    全局信息特征处理类
    处理游戏的全局状态信息，包括游戏时间、金币、英雄数量、击杀数、防御塔等
    """
    def __init__(self, camp):
        self.normalizer = FeatureNormalizer()
        self.main_camp = camp
        self.transform_camp2_to_camp1 = camp == "PLAYERCAMP_2"
        self.get_global_config()
        self.map_feature_to_norm = self.normalizer.parse_config(self.global_feature_config)

        # 全局特征配置 - 根据您提供的特征表格
        self.global_features = [
            'g_game_time',      # 游戏时间 - 5维
            'g_friend_money',   # 友方金币 - 6维
            'g_enemy_money',    # 敌方金币 - 6维
            'g_money_diff',     # 金币差 - 5维
            'g_friend_hero',    # 友方英雄 - 5维
            'g_enemy_hero',     # 敌方英雄 - 5维
            'g_hero_diff',      # 英雄差 - 11维
            'g_friend_kill',    # 友方击杀 - 3维
            'g_enemy_kill',     # 敌方击杀 - 3维
            'g_kill_diff',      # 击杀差 - 5维
            'g_friend_organ',   # 友方塔 - 4维
            'g_enemy_organ',    # 敌方塔 - 4维
            'g_organ_diff',     # 塔数差 - 6维
            'g_frame_no_div_1000',  # 帧号/1000 - 1维
            'g_frame_no_mod_1000',  # 帧号%1000 - 1维
        ]

    def get_global_config(self):
        """加载全局特征配置"""
        self.config = configparser.ConfigParser()
        current_dir = os.path.dirname(__file__)
        config_path = os.path.join(current_dir, "global_feature_config.ini")
        self.config.read(config_path)

        # 获取归一化的配置
        self.global_feature_config = []
        for feature, config in self.config["feature_config"].items():
            self.global_feature_config.append(f"{feature}:{config}")

        # 获取特征函数的配置
        self.feature_func_map = {}
        for feature, func_name in self.config["feature_functions"].items():
            if hasattr(self, func_name):
                self.feature_func_map[feature] = getattr(self, func_name)
            else:
                raise ValueError(f"Unsupported function: {func_name}")

    def process_global_features(self, frame_state):
        """
        处理全局特征
        Args:
            frame_state: 游戏状态数据
        Returns:
            list: 全局特征向量
        """
        global_vector_feature = []

        # 获取基础数据
        frame_no = frame_state.get("frameNo", 0)
        hero_states = frame_state.get("hero_states", [])
        npc_states = frame_state.get("npc_states", [])

        # 分析双方数据
        friend_data, enemy_data = self._analyze_camps_data(hero_states, npc_states)

        # 生成各个全局特征
        for feature_name in self.global_features:
            if feature_name in self.feature_func_map:
                feature_values = []
                self.feature_func_map[feature_name](frame_state, friend_data, enemy_data, feature_values)

                # 归一化特征
                if feature_name in self.map_feature_to_norm:
                    for value in feature_values:
                        norm_func, *params = self.map_feature_to_norm[feature_name]
                        normalized_value = norm_func(value, *params)
                        if isinstance(normalized_value, list):
                            global_vector_feature.extend(normalized_value)
                        else:
                            global_vector_feature.append(normalized_value)
                else:
                    global_vector_feature.extend(feature_values)

        return global_vector_feature

    def _analyze_camps_data(self, hero_states, npc_states):
        """
        分析双方阵营数据
        Args:
            hero_states: 英雄状态列表
            npc_states: NPC状态列表
        Returns:
            tuple: (友方数据, 敌方数据)
        """
        friend_data = {
            'heroes': [],
            'money': 0,
            'kills': 0,
            'towers': 0,
            'alive_heroes': 0
        }

        enemy_data = {
            'heroes': [],
            'money': 0,
            'kills': 0,
            'towers': 0,
            'alive_heroes': 0
        }

        # 分析英雄数据
        for hero in hero_states:
            hero_camp = hero.get("actor_state", {}).get("camp")
            if hero_camp == self.main_camp:
                friend_data['heroes'].append(hero)
                if hero.get("actor_state", {}).get("hp", 0) > 0:
                    friend_data['alive_heroes'] += 1
                # 假设金币和击杀数在hero数据中
                friend_data['money'] += hero.get("actor_state", {}).get("money", 0)
                friend_data['kills'] += hero.get("actor_state", {}).get("kill", 0)
            else:
                enemy_data['heroes'].append(hero)
                if hero.get("actor_state", {}).get("hp", 0) > 0:
                    enemy_data['alive_heroes'] += 1
                enemy_data['money'] += hero.get("actor_state", {}).get("money", 0)
                enemy_data['kills'] += hero.get("actor_state", {}).get("kill", 0)

        # 分析防御塔数据
        for npc in npc_states:
            if npc.get("sub_type") == "ACTOR_SUB_TOWER" and npc.get("hp", 0) > 0:
                npc_camp = npc.get("camp")
                if npc_camp == self.main_camp:
                    friend_data['towers'] += 1
                else:
                    enemy_data['towers'] += 1

        return friend_data, enemy_data

    # ==================== 全局特征处理函数 ====================

    def g_game_time(self, frame_state, friend_data, enemy_data, feature_values):
        """
        游戏时间特征 - 5维
        描述：将游戏前10分钟平均分为5段，超过10分钟的都归为最后一段
        """
        frame_no = frame_state.get("frameNo", 0)
        # 假设游戏帧率为30fps，10分钟 = 600秒 = 18000帧
        game_time_seconds = frame_no / 30.0

        # 将前10分钟(600秒)分为5段，每段120秒
        if game_time_seconds <= 120:
            segment = 0
        elif game_time_seconds <= 240:
            segment = 1
        elif game_time_seconds <= 360:
            segment = 2
        elif game_time_seconds <= 480:
            segment = 3
        elif game_time_seconds <= 600:
            segment = 4
        else:
            segment = 4  # 超过10分钟的都归为最后一段

        # 生成5维one-hot编码
        for i in range(5):
            feature_values.append(1.0 if i == segment else 0.0)

    def g_friend_money(self, frame_state, friend_data, enemy_data, feature_values):
        """友方金币特征 - 6维"""
        money = friend_data['money']
        # 将金币数量分为6个区间进行编码
        if money < 1000:
            segment = 0
        elif money < 2000:
            segment = 1
        elif money < 3000:
            segment = 2
        elif money < 5000:
            segment = 3
        elif money < 8000:
            segment = 4
        else:
            segment = 5

        for i in range(6):
            feature_values.append(1.0 if i == segment else 0.0)

    def g_enemy_money(self, frame_state, friend_data, enemy_data, feature_values):
        """敌方金币特征 - 6维"""
        money = enemy_data['money']
        # 将金币数量分为6个区间进行编码
        if money < 1000:
            segment = 0
        elif money < 2000:
            segment = 1
        elif money < 3000:
            segment = 2
        elif money < 5000:
            segment = 3
        elif money < 8000:
            segment = 4
        else:
            segment = 5

        for i in range(6):
            feature_values.append(1.0 if i == segment else 0.0)

    def g_money_diff(self, frame_state, friend_data, enemy_data, feature_values):
        """金币差特征 - 5维"""
        money_diff = friend_data['money'] - enemy_data['money']

        # 将金币差分为5个区间
        if money_diff < -2000:
            segment = 0  # 大幅落后
        elif money_diff < -500:
            segment = 1  # 落后
        elif money_diff <= 500:
            segment = 2  # 平衡
        elif money_diff <= 2000:
            segment = 3  # 领先
        else:
            segment = 4  # 大幅领先

        for i in range(5):
            feature_values.append(1.0 if i == segment else 0.0)

    def g_friend_hero(self, frame_state, friend_data, enemy_data, feature_values):
        """友方英雄特征 - 5维"""
        alive_heroes = friend_data['alive_heroes']
        total_heroes = len(friend_data['heroes'])

        # 根据存活英雄数量编码
        for i in range(5):
            if i < alive_heroes:
                feature_values.append(1.0)
            else:
                feature_values.append(0.0)

    def g_enemy_hero(self, frame_state, friend_data, enemy_data, feature_values):
        """敌方英雄特征 - 5维"""
        alive_heroes = enemy_data['alive_heroes']
        total_heroes = len(enemy_data['heroes'])

        # 根据存活英雄数量编码
        for i in range(5):
            if i < alive_heroes:
                feature_values.append(1.0)
            else:
                feature_values.append(0.0)

    def g_hero_diff(self, frame_state, friend_data, enemy_data, feature_values):
        """英雄差特征 - 11维"""
        hero_diff = friend_data['alive_heroes'] - enemy_data['alive_heroes']

        # 英雄差范围从-5到+5，映射到0-10的索引
        diff_index = max(0, min(10, hero_diff + 5))

        for i in range(11):
            feature_values.append(1.0 if i == diff_index else 0.0)

    def g_friend_kill(self, frame_state, friend_data, enemy_data, feature_values):
        """友方击杀特征 - 3维"""
        kills = friend_data['kills']

        # 将击杀数分为3个区间
        if kills < 5:
            segment = 0
        elif kills < 15:
            segment = 1
        else:
            segment = 2

        for i in range(3):
            feature_values.append(1.0 if i == segment else 0.0)

    def g_enemy_kill(self, frame_state, friend_data, enemy_data, feature_values):
        """敌方击杀特征 - 3维"""
        kills = enemy_data['kills']

        # 将击杀数分为3个区间
        if kills < 5:
            segment = 0
        elif kills < 15:
            segment = 1
        else:
            segment = 2

        for i in range(3):
            feature_values.append(1.0 if i == segment else 0.0)

    def g_kill_diff(self, frame_state, friend_data, enemy_data, feature_values):
        """击杀差特征 - 5维"""
        kill_diff = friend_data['kills'] - enemy_data['kills']

        # 将击杀差分为5个区间
        if kill_diff < -10:
            segment = 0  # 大幅落后
        elif kill_diff < -3:
            segment = 1  # 落后
        elif kill_diff <= 3:
            segment = 2  # 平衡
        elif kill_diff <= 10:
            segment = 3  # 领先
        else:
            segment = 4  # 大幅领先

        for i in range(5):
            feature_values.append(1.0 if i == segment else 0.0)

    def g_friend_organ(self, frame_state, friend_data, enemy_data, feature_values):
        """友方塔特征 - 4维"""
        towers = friend_data['towers']

        # 根据防御塔数量编码（假设最多4座塔）
        for i in range(4):
            if i < towers:
                feature_values.append(1.0)
            else:
                feature_values.append(0.0)

    def g_enemy_organ(self, frame_state, friend_data, enemy_data, feature_values):
        """敌方塔特征 - 4维"""
        towers = enemy_data['towers']

        # 根据防御塔数量编码（假设最多4座塔）
        for i in range(4):
            if i < towers:
                feature_values.append(1.0)
            else:
                feature_values.append(0.0)

    def g_organ_diff(self, frame_state, friend_data, enemy_data, feature_values):
        """塔数差特征 - 6维"""
        tower_diff = friend_data['towers'] - enemy_data['towers']

        # 塔数差范围从-4到+4，映射到0-5的索引（压缩范围）
        if tower_diff < -2:
            segment = 0
        elif tower_diff == -1:
            segment = 1
        elif tower_diff == 0:
            segment = 2
        elif tower_diff == 1:
            segment = 3
        elif tower_diff == 2:
            segment = 4
        else:  # tower_diff > 2
            segment = 5

        for i in range(6):
            feature_values.append(1.0 if i == segment else 0.0)

    def g_frame_no_div_1000(self, frame_state, friend_data, enemy_data, feature_values):
        """帧号/1000特征 - 1维"""
        frame_no = frame_state.get("frameNo", 0)
        value = frame_no // 1000
        feature_values.append(float(value))

    def g_frame_no_mod_1000(self, frame_state, friend_data, enemy_data, feature_values):
        """帧号%1000特征 - 1维"""
        frame_no = frame_state.get("frameNo", 0)
        value = frame_no % 1000
        feature_values.append(float(value))


class OrganProcess:
    def __init__(self, camp):
        self.normalizer = FeatureNormalizer()
        self.main_camp = camp

        self.main_camp_hero_dict = {}
        self.enemy_camp_hero_dict = {}
        self.main_camp_organ_dict = {}
        self.enemy_camp_organ_dict = {}

        self.transform_camp2_to_camp1 = camp == "PLAYERCAMP_2"
        self.get_organ_config()
        self.map_feature_to_norm = self.normalizer.parse_config(self.organ_feature_config)
        self.view_dist = 15000
        self.one_unit_feature_num = 7
        self.unit_buff_num = 1

    def get_organ_config(self):
        self.config = configparser.ConfigParser()
        current_dir = os.path.dirname(__file__)
        config_path = os.path.join(current_dir, "organ_feature_config.ini")
        self.config.read(config_path)

        # Get normalized configuration
        # 获取归一化的配置
        self.organ_feature_config = []
        for feature, config in self.config["feature_config"].items():
            self.organ_feature_config.append(f"{feature}:{config}")

        # Get feature function configuration
        # 获取特征函数的配置
        self.feature_func_map = {}
        for feature, func_name in self.config["feature_functions"].items():
            if hasattr(self, func_name):
                self.feature_func_map[feature] = getattr(self, func_name)
            else:
                raise ValueError(f"Unsupported function: {func_name}")

    def process_vec_organ(self, frame_state):
        self.generate_organ_info_dict(frame_state)
        self.generate_hero_info_list(frame_state)

        local_vector_feature = []

        # Generate features for enemy team's towers
        # 生成敌方阵营的防御塔特征
        enemy_camp_organ_vector_feature = self.generate_one_type_organ_feature(self.enemy_camp_organ_dict, "enemy_camp")
        local_vector_feature.extend(enemy_camp_organ_vector_feature)

        vector_feature = local_vector_feature
        return vector_feature

    def generate_hero_info_list(self, frame_state):
        self.main_camp_hero_dict.clear()
        self.enemy_camp_hero_dict.clear()
        for hero in frame_state["hero_states"]:
            if hero["actor_state"]["camp"] == self.main_camp:
                self.main_camp_hero_dict[hero["actor_state"]["config_id"]] = hero
                self.main_hero_info = hero
            else:
                self.enemy_camp_hero_dict[hero["actor_state"]["config_id"]] = hero

    def generate_organ_info_dict(self, frame_state):
        self.main_camp_organ_dict.clear()
        self.enemy_camp_organ_dict.clear()

        for organ in frame_state["npc_states"]:
            organ_camp = organ["camp"]
            organ_subtype = organ["sub_type"]
            if organ_camp == self.main_camp:
                if organ_subtype == "ACTOR_SUB_TOWER":
                    self.main_camp_organ_dict["tower"] = organ
            else:
                if organ_subtype == "ACTOR_SUB_TOWER":
                    self.enemy_camp_organ_dict["tower"] = organ

    def generate_one_type_organ_feature(self, one_type_organ_info, camp):
        vector_feature = []
        num_organs_considered = 0

        def process_organ(organ):
            nonlocal num_organs_considered
            # Generate each specific feature through feature_func_map
            # 通过 feature_func_map 生成每个具体特征
            for feature_name, feature_func in self.feature_func_map.items():
                value = []
                self.feature_func_map[feature_name](organ, value)
                # Normalize the specific features
                # 对具体特征进行正则化
                if feature_name not in self.map_feature_to_norm:
                    assert False
                for k in value:
                    norm_func, *params = self.map_feature_to_norm[feature_name]
                    normalized_value = norm_func(k, *params)
                    if isinstance(normalized_value, list):
                        vector_feature.extend(normalized_value)
                    else:
                        vector_feature.append(normalized_value)
            num_organs_considered += 1

        if "tower" in one_type_organ_info:
            organ = one_type_organ_info["tower"]
            process_organ(organ)

        if num_organs_considered < self.unit_buff_num:
            self.no_organ_feature(vector_feature, num_organs_considered)
        return vector_feature

    def no_organ_feature(self, vector_feature, num_organs_considered):
        for _ in range((self.unit_buff_num - num_organs_considered) * self.one_unit_feature_num):
            vector_feature.append(0)

    def get_hp_rate(self, organ, vector_feature):
        value = 0
        if organ["max_hp"] > 0:
            value = organ["hp"] / organ["max_hp"]
        vector_feature.append(value)

    def judge_in_view(self, main_hero_location, obj_location):
        if (
            (main_hero_location["x"] - obj_location["x"] >= 0 - self.view_dist)
            and (main_hero_location["x"] - obj_location["x"] <= self.view_dist)
            and (main_hero_location["z"] - obj_location["z"] >= 0 - self.view_dist)
            and (main_hero_location["z"] - obj_location["z"] <= self.view_dist)
        ):
            return True
        return False

    def cal_dist(self, pos1, pos2):
        dist = math.sqrt((pos1["x"] / 100.0 - pos2["x"] / 100.0) ** 2 + (pos1["z"] / 100.0 - pos2["z"] / 100.0) ** 2)
        return dist

    def is_alive(self, organ, vector_feature):
        value = 0.0
        if organ["hp"] > 0:
            value = 1.0
        vector_feature.append(value)

    def belong_to_main_camp(self, organ, vector_feature):
        value = 0.0
        if organ["camp"] == self.main_hero_info["actor_state"]["camp"]:
            value = 1.0
        vector_feature.append(value)

    def get_normal_organ_location_x(self, organ, vector_feature):
        value = organ["location"]["x"]
        if self.transform_camp2_to_camp1:
            value = 0 - value
        vector_feature.append(value)

    def get_normal_organ_location_z(self, organ, vector_feature):
        value = organ["location"]["z"]
        if self.transform_camp2_to_camp1:
            value = 0 - value
        vector_feature.append(value)

    def relative_location_x(self, organ, vector_feature):
        organ_location_x = organ["location"]["x"]
        location_x = self.main_hero_info["actor_state"]["location"]["x"]
        x_diff = organ_location_x - location_x
        if self.transform_camp2_to_camp1 and organ_location_x != 100000:
            x_diff = -x_diff
        value = (x_diff + 15000) / 30000.0
        vector_feature.append(value)

    def relative_location_z(self, organ, vector_feature):
        organ_location_z = organ["location"]["z"]
        location_z = self.main_hero_info["actor_state"]["location"]["z"]
        z_diff = organ_location_z - location_z
        if self.transform_camp2_to_camp1 and organ_location_z != 100000:
            z_diff = -z_diff
        value = (z_diff + 15000) / 30000.0
        vector_feature.append(value)
