# 王者荣耀1v1金币特征划分方案

## 基础参数
- **最大金币数量**: 12000
- **金币差范围**: [-12000, +12000]
- **特征维度**: 金币差5维，单方金币6维

## 1. 金币差特征 (5维) - 优化方案

### 划分逻辑
```python
if money_diff < -4000:
    segment = 0  # 大幅落后 (装备差距很大，处于危险状态)
elif money_diff < -1500:
    segment = 1  # 落后 (有明显劣势，需要谨慎)
elif money_diff <= 1500:
    segment = 2  # 平衡 (双方实力相当，±1500以内)
elif money_diff <= 4000:
    segment = 3  # 领先 (有装备优势，但不是压倒性)
else:
    segment = 4  # 大幅领先 (装备优势明显，胜率很高)
```

### 各段分析

| 段位 | 金币差范围 | 游戏意义 | 战术建议 | 覆盖比例 |
|------|------------|----------|----------|----------|
| 0 | < -4000 | 大幅落后 | 避战发育，寻找翻盘机会 | ~15% |
| 1 | -4000 ~ -1501 | 落后 | 谨慎对战，避免进一步扩大差距 | ~20% |
| 2 | -1500 ~ +1500 | 平衡 | 正常对战，技术决定胜负 | ~30% |
| 3 | +1501 ~ +4000 | 领先 | 积极进攻，扩大优势 | ~20% |
| 4 | > +4000 | 大幅领先 | 主动出击，结束比赛 | ~15% |

### 优化理由

1. **±1500平衡区间**: 
   - 对应1-2件小装备的差距
   - 技术操作仍能弥补装备差距
   - 符合1v1对战的平衡性

2. **±4000临界点**:
   - 对应1件大装备的差距
   - 装备优势开始显著影响胜负
   - 战术策略需要明显调整

## 2. 单方金币特征 (6维) - 优化方案

### 划分逻辑
```python
if money < 2000:
    segment = 0  # 前期 (0-1999)
elif money < 4000:
    segment = 1  # 发育期 (2000-3999)
elif money < 6000:
    segment = 2  # 中期 (4000-5999)
elif money < 8000:
    segment = 3  # 中后期 (6000-7999)
elif money < 10000:
    segment = 4  # 后期 (8000-9999)
else:
    segment = 5  # 满装备期 (10000+)
```

### 各段分析

| 段位 | 金币范围 | 游戏阶段 | 装备水平 | 战斗力 |
|------|----------|----------|----------|--------|
| 0 | 0-1999 | 前期 | 基础装备 | 较弱 |
| 1 | 2000-3999 | 发育期 | 1-2件小装备 | 一般 |
| 2 | 4000-5999 | 中期 | 核心装备成型 | 中等 |
| 3 | 6000-7999 | 中后期 | 2-3件大装备 | 较强 |
| 4 | 8000-9999 | 后期 | 接近满装 | 很强 |
| 5 | 10000+ | 满装备期 | 满装备 | 最强 |

### 优化理由

1. **2000金币间隔**: 
   - 对应王者荣耀中一件中等装备的价格
   - 每个阶段都有明确的装备意义
   - 便于AI理解装备进度

2. **10000+封顶**:
   - 考虑到实际游戏中很少达到12000满金币
   - 10000+已经是接近满装状态
   - 避免过细的划分导致数据稀疏

## 3. 与原方案对比

### 原方案问题
- **金币差**: ±500/±2000的划分过于保守，不适合12000金币上限
- **单方金币**: 1000/2000/3000/5000/8000的划分不够均匀

### 新方案优势
1. **更合理的区间**: 基于12000金币上限重新设计
2. **游戏意义明确**: 每个区间都对应明确的游戏状态
3. **平衡的分布**: 各区间覆盖比例相对均匀
4. **战术指导性**: 每个区间都有明确的战术含义

## 4. 实际应用建议

### 训练阶段
- 收集实际游戏数据，验证各区间的分布情况
- 根据胜率统计调整临界点
- 考虑不同英雄的金币获取速度差异

### 调优方向
1. **动态调整**: 根据游戏版本更新调整划分
2. **英雄特化**: 不同英雄可能需要不同的金币划分
3. **时间因素**: 考虑游戏时长对金币意义的影响

## 5. 代码实现

已在 `global_process copy.py` 中实现：
- `get_global_money_diff()`: 5维金币差特征
- `get_global_friend_money()`: 6维友方金币特征  
- `get_global_enemy_money()`: 6维敌方金币特征

所有特征都使用one-hot编码，确保特征的稳定性和可解释性。
