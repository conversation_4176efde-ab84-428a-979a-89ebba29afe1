#!/usr/bin/env python3
"""
简单测试全局特征处理
"""

# 模拟VecCampsWholeInfo类的核心逻辑
def test_game_time_logic():
    """测试游戏时间特征逻辑"""
    print("=== 测试游戏时间特征逻辑 ===")
    
    def calculate_game_time_feature(frame_no):
        """计算游戏时间特征"""
        game_time_seconds = frame_no / 30.0
        
        if game_time_seconds <= 120:
            segment = 0
        elif game_time_seconds <= 240:
            segment = 1
        elif game_time_seconds <= 360:
            segment = 2
        elif game_time_seconds <= 480:
            segment = 3
        elif game_time_seconds <= 600:
            segment = 4
        else:
            segment = 4
        
        # 生成5维one-hot编码
        feature_values = []
        for i in range(5):
            feature_values.append(1.0 if i == segment else 0.0)
        
        return feature_values
    
    # 测试不同时间点
    test_cases = [
        (0, "0秒"),
        (3000, "100秒"),
        (5400, "180秒"),
        (10800, "360秒"),
        (14400, "480秒"),
        (18000, "600秒"),
        (27000, "900秒")
    ]
    
    for frame_no, description in test_cases:
        feature = calculate_game_time_feature(frame_no)
        print(f"{description} (帧号{frame_no}): {feature}")

def test_money_feature_logic():
    """测试金币特征逻辑"""
    print("\n=== 测试金币特征逻辑 ===")
    
    def calculate_money_feature(money):
        """计算金币特征"""
        if money < 1000:
            segment = 0
        elif money < 2000:
            segment = 1
        elif money < 3000:
            segment = 2
        elif money < 5000:
            segment = 3
        elif money < 8000:
            segment = 4
        else:
            segment = 5
        
        feature_values = []
        for i in range(6):
            feature_values.append(1.0 if i == segment else 0.0)
        
        return feature_values
    
    test_cases = [500, 1500, 2500, 4000, 6000, 10000]
    
    for money in test_cases:
        feature = calculate_money_feature(money)
        print(f"金币{money}: {feature}")

def test_diff_feature_logic():
    """测试差值特征逻辑"""
    print("\n=== 测试金币差特征逻辑 ===")
    
    def calculate_money_diff_feature(money_diff):
        """计算金币差特征"""
        if money_diff < -2000:
            segment = 0
        elif money_diff < -500:
            segment = 1
        elif money_diff <= 500:
            segment = 2
        elif money_diff <= 2000:
            segment = 3
        else:
            segment = 4
        
        feature_values = []
        for i in range(5):
            feature_values.append(1.0 if i == segment else 0.0)
        
        return feature_values
    
    test_cases = [-3000, -1000, 0, 1000, 3000]
    
    for diff in test_cases:
        feature = calculate_money_diff_feature(diff)
        print(f"金币差{diff}: {feature}")

def test_hero_diff_feature_logic():
    """测试英雄差特征逻辑"""
    print("\n=== 测试英雄差特征逻辑 ===")
    
    def calculate_hero_diff_feature(hero_diff):
        """计算英雄差特征"""
        # 英雄差范围从-5到+5，映射到0-10的索引
        diff_index = max(0, min(10, hero_diff + 5))
        
        feature_values = []
        for i in range(11):
            feature_values.append(1.0 if i == diff_index else 0.0)
        
        return feature_values
    
    test_cases = [-5, -2, 0, 2, 5]
    
    for diff in test_cases:
        feature = calculate_hero_diff_feature(diff)
        print(f"英雄差{diff}: {feature}")

def test_frame_features():
    """测试帧号特征"""
    print("\n=== 测试帧号特征 ===")
    
    test_frame_nos = [0, 999, 1000, 1500, 5000, 15000]
    
    for frame_no in test_frame_nos:
        div_1000 = frame_no // 1000
        mod_1000 = frame_no % 1000
        print(f"帧号{frame_no}: div_1000={div_1000}, mod_1000={mod_1000}")

def calculate_total_dimensions():
    """计算总特征维度"""
    print("\n=== 特征维度统计 ===")
    
    feature_dims = {
        'g_game_time': 5,
        'g_friend_money': 6,
        'g_enemy_money': 6,
        'g_money_diff': 5,
        'g_friend_hero': 5,
        'g_enemy_hero': 5,
        'g_hero_diff': 11,
        'g_friend_kill': 3,
        'g_enemy_kill': 3,
        'g_kill_diff': 5,
        'g_friend_organ': 4,
        'g_enemy_organ': 4,
        'g_organ_diff': 6,
        'g_frame_no_div_1000': 1,
        'g_frame_no_mod_1000': 1
    }
    
    total_dims = 0
    for feature_name, dims in feature_dims.items():
        print(f"{feature_name}: {dims}维")
        total_dims += dims
    
    print(f"\n总维度: {total_dims}维")
    
    # 验证与您提供的表格是否一致
    expected_total = 70  # 根据您的表格计算
    if total_dims == expected_total:
        print("✓ 维度计算正确")
    else:
        print(f"✗ 维度不匹配，期望{expected_total}，实际{total_dims}")

if __name__ == "__main__":
    test_game_time_logic()
    test_money_feature_logic()
    test_diff_feature_logic()
    test_hero_diff_feature_logic()
    test_frame_features()
    calculate_total_dimensions()
