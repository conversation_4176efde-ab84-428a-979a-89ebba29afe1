#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
测试全局特征处理功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'agent_ppo'))

from agent_ppo.feature.feature_process.global_process import VecCampsWholeInfo

def create_mock_frame_state():
    """创建模拟的frame_state数据"""
    return {
        "frameNo": 15000,  # 500秒，约8.3分钟
        "hero_states": [
            {
                "player_id": 1,
                "actor_state": {
                    "camp": "PLAYERCAMP_1",
                    "config_id": 169,
                    "hp": 8000,
                    "max_hp": 10000,
                    "money": 3500,
                    "kill": 8,
                    "location": {"x": 1000, "z": 2000}
                }
            },
            {
                "player_id": 2,
                "actor_state": {
                    "camp": "PLAYERCAMP_2",
                    "config_id": 173,
                    "hp": 6000,
                    "max_hp": 9000,
                    "money": 2800,
                    "kill": 5,
                    "location": {"x": -1000, "z": -2000}
                }
            }
        ],
        "npc_states": [
            {
                "sub_type": "ACTOR_SUB_TOWER",
                "camp": "PLAYERCAMP_1",
                "hp": 5000,
                "max_hp": 8000,
                "location": {"x": 500, "z": 1000}
            },
            {
                "sub_type": "ACTOR_SUB_TOWER",
                "camp": "PLAYERCAMP_2",
                "hp": 3000,
                "max_hp": 8000,
                "location": {"x": -500, "z": -1000}
            },
            {
                "sub_type": "ACTOR_SUB_CRYSTAL",
                "camp": "PLAYERCAMP_1",
                "hp": 10000,
                "max_hp": 10000,
                "location": {"x": 0, "z": 0}
            }
        ]
    }

def test_global_features():
    """测试全局特征处理"""
    print("=== 测试全局特征处理 ===")
    
    # 创建全局特征处理器
    global_processor = VecCampsWholeInfo("PLAYERCAMP_1")
    
    # 创建模拟数据
    frame_state = create_mock_frame_state()
    
    # 处理全局特征
    try:
        global_features = global_processor.process_global_features(frame_state)
        
        print(f"全局特征总维度: {len(global_features)}")
        print(f"全局特征向量: {global_features}")
        
        # 验证特征维度
        expected_dims = {
            'g_game_time': 5,
            'g_friend_money': 6,
            'g_enemy_money': 6,
            'g_money_diff': 5,
            'g_friend_hero': 5,
            'g_enemy_hero': 5,
            'g_hero_diff': 11,
            'g_friend_kill': 3,
            'g_enemy_kill': 3,
            'g_kill_diff': 5,
            'g_friend_organ': 4,
            'g_enemy_organ': 4,
            'g_organ_diff': 6,
            'g_frame_no_div_1000': 1,
            'g_frame_no_mod_1000': 1
        }
        
        total_expected_dims = sum(expected_dims.values())
        print(f"期望总维度: {total_expected_dims}")
        
        if len(global_features) == total_expected_dims:
            print("✓ 特征维度正确")
        else:
            print(f"✗ 特征维度不匹配，期望{total_expected_dims}，实际{len(global_features)}")
        
        # 详细分析每个特征
        print("\n=== 特征详细分析 ===")
        idx = 0
        for feature_name, dim in expected_dims.items():
            feature_values = global_features[idx:idx+dim]
            print(f"{feature_name} ({dim}维): {feature_values}")
            idx += dim
            
    except Exception as e:
        print(f"处理全局特征时出错: {e}")
        import traceback
        traceback.print_exc()

def test_game_time_feature():
    """测试游戏时间特征的具体逻辑"""
    print("\n=== 测试游戏时间特征 ===")
    
    global_processor = VecCampsWholeInfo("PLAYERCAMP_1")
    
    # 测试不同时间段
    test_cases = [
        (0, "0秒 - 第1段"),
        (3000, "100秒 - 第1段"),
        (5400, "180秒 - 第2段"),
        (10800, "360秒 - 第3段"),
        (14400, "480秒 - 第4段"),
        (18000, "600秒 - 第5段"),
        (27000, "900秒 - 第5段(超过10分钟)")
    ]
    
    for frame_no, description in test_cases:
        frame_state = {"frameNo": frame_no, "hero_states": [], "npc_states": []}
        friend_data = {"money": 0, "kills": 0, "towers": 0, "alive_heroes": 0, "heroes": []}
        enemy_data = {"money": 0, "kills": 0, "towers": 0, "alive_heroes": 0, "heroes": []}
        
        feature_values = []
        global_processor.g_game_time(frame_state, friend_data, enemy_data, feature_values)
        
        print(f"{description}: {feature_values}")

if __name__ == "__main__":
    test_global_features()
    test_game_time_feature()
