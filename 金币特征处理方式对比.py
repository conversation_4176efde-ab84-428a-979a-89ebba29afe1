#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
金币特征处理的三种方式对比
"""

class GlobalMoneyFeatureComparison:
    """展示三种不同的金币特征处理方式"""
    
    def __init__(self, camp):
        self.main_camp = camp
        # 用于方式2的数据存储
        self.main_camp_hero_dict = {}
        self.enemy_camp_hero_dict = {}
    
    # ==================== 方式1: 使用预计算数据（推荐） ====================
    def method1_use_precalculated_data(self, frame_state, friend_data, enemy_data, feature_values):
        """
        方式1: 使用预计算的数据（推荐）
        优点: 性能最好，数据已经在_analyze_camps_data中计算过
        缺点: 依赖于预计算的数据结构
        """
        friend_money = friend_data['money']
        enemy_money = enemy_data['money']
        money_diff = friend_money - enemy_money
        
        # 金币差分段
        if money_diff < -2000:
            segment = 0
        elif money_diff < -500:
            segment = 1
        elif money_diff <= 500:
            segment = 2
        elif money_diff <= 2000:
            segment = 3
        else:
            segment = 4
        
        for i in range(5):
            feature_values.append(1.0 if i == segment else 0.0)
    
    # ==================== 方式2: 类似hero_process格式 ====================
    def method2_hero_process_style(self, frame_state, feature_values):
        """
        方式2: 类似hero_process.py的格式
        优点: 与现有代码风格一致，便于维护
        缺点: 需要额外的数据结构和计算步骤
        """
        # 步骤1: 生成英雄信息字典（类似hero_process）
        self.generate_hero_money_info(frame_state)
        
        # 步骤2: 从字典计算金币
        friend_money = self.calculate_camp_money_from_hero_dict(self.main_camp_hero_dict)
        enemy_money = self.calculate_camp_money_from_hero_dict(self.enemy_camp_hero_dict)
        money_diff = friend_money - enemy_money
        
        # 步骤3: 生成特征
        if money_diff < -2000:
            segment = 0
        elif money_diff < -500:
            segment = 1
        elif money_diff <= 500:
            segment = 2
        elif money_diff <= 2000:
            segment = 3
        else:
            segment = 4
        
        for i in range(5):
            feature_values.append(1.0 if i == segment else 0.0)
    
    def generate_hero_money_info(self, frame_state):
        """生成英雄金币信息字典"""
        self.main_camp_hero_dict.clear()
        self.enemy_camp_hero_dict.clear()
        
        for hero in frame_state["hero_states"]:
            hero_camp = hero["actor_state"]["camp"]
            hero_config_id = hero["actor_state"]["config_id"]
            
            if hero_camp == self.main_camp:
                self.main_camp_hero_dict[hero_config_id] = hero
            else:
                self.enemy_camp_hero_dict[hero_config_id] = hero
    
    def calculate_camp_money_from_hero_dict(self, hero_dict):
        """从英雄字典计算阵营总金币"""
        total_money = 0
        for hero in hero_dict.values():
            hero_money = hero.get("actor_state", {}).get("money", 0)
            total_money += hero_money
        return total_money
    
    # ==================== 方式3: 直接从frame_state计算 ====================
    def method3_direct_calculation(self, frame_state, feature_values):
        """
        方式3: 直接从frame_state计算
        优点: 最直接，不依赖其他数据结构
        缺点: 每次都需要遍历所有英雄
        """
        friend_money = 0
        enemy_money = 0
        
        # 直接遍历计算
        for hero in frame_state.get("hero_states", []):
            hero_camp = hero.get("actor_state", {}).get("camp")
            hero_money = hero.get("actor_state", {}).get("money", 0)
            
            if hero_camp == self.main_camp:
                friend_money += hero_money
            else:
                enemy_money += hero_money
        
        money_diff = friend_money - enemy_money
        
        # 生成特征
        if money_diff < -2000:
            segment = 0
        elif money_diff < -500:
            segment = 1
        elif money_diff <= 500:
            segment = 2
        elif money_diff <= 2000:
            segment = 3
        else:
            segment = 4
        
        for i in range(5):
            feature_values.append(1.0 if i == segment else 0.0)

def test_three_methods():
    """测试三种方法的效果"""
    # 模拟数据
    frame_state = {
        "frameNo": 15000,
        "hero_states": [
            {
                "actor_state": {
                    "camp": "PLAYERCAMP_1",
                    "config_id": 169,
                    "money": 3500
                }
            },
            {
                "actor_state": {
                    "camp": "PLAYERCAMP_2", 
                    "config_id": 173,
                    "money": 2800
                }
            }
        ]
    }
    
    # 预计算数据（方式1需要）
    friend_data = {'money': 3500}
    enemy_data = {'money': 2800}
    
    processor = GlobalMoneyFeatureComparison("PLAYERCAMP_1")
    
    # 测试方式1
    feature_values_1 = []
    processor.method1_use_precalculated_data(frame_state, friend_data, enemy_data, feature_values_1)
    print(f"方式1结果: {feature_values_1}")
    
    # 测试方式2
    feature_values_2 = []
    processor.method2_hero_process_style(frame_state, feature_values_2)
    print(f"方式2结果: {feature_values_2}")
    
    # 测试方式3
    feature_values_3 = []
    processor.method3_direct_calculation(frame_state, feature_values_3)
    print(f"方式3结果: {feature_values_3}")
    
    # 验证结果一致性
    if feature_values_1 == feature_values_2 == feature_values_3:
        print("✓ 三种方法结果一致")
    else:
        print("✗ 方法结果不一致")

if __name__ == "__main__":
    test_three_methods()
