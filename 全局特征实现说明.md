# 全局特征实现说明

## 概述

根据您提供的特征表格，我已经实现了完整的全局特征处理系统。该系统包含15个全局特征，总共70维特征向量。

## 特征详细说明

### 1. g_game_time (游戏时间) - 5维
**描述**: 将游戏前10分钟平均分为5段，超过10分钟的都归为最后一段
**实现逻辑**:
- 假设游戏帧率为30fps
- 前10分钟(600秒)分为5段，每段120秒
- 使用one-hot编码表示当前时间段
- 0-120秒: [1,0,0,0,0]
- 121-240秒: [0,1,0,0,0]
- 241-360秒: [0,0,1,0,0]
- 361-480秒: [0,0,0,1,0]
- 481秒以上: [0,0,0,0,1]

### 2. g_friend_money (友方金币) - 6维
**描述**: 友方阵营的总金币数量
**实现逻辑**:
- 将金币数量分为6个区间
- <1000: [1,0,0,0,0,0]
- 1000-1999: [0,1,0,0,0,0]
- 2000-2999: [0,0,1,0,0,0]
- 3000-4999: [0,0,0,1,0,0]
- 5000-7999: [0,0,0,0,1,0]
- ≥8000: [0,0,0,0,0,1]

### 3. g_enemy_money (敌方金币) - 6维
**描述**: 敌方阵营的总金币数量
**实现逻辑**: 与友方金币相同的区间划分

### 4. g_money_diff (金币差) - 5维
**描述**: 友方金币 - 敌方金币
**实现逻辑**:
- <-2000 (大幅落后): [1,0,0,0,0]
- -2000到-501 (落后): [0,1,0,0,0]
- -500到500 (平衡): [0,0,1,0,0]
- 501到2000 (领先): [0,0,0,1,0]
- >2000 (大幅领先): [0,0,0,0,1]

### 5. g_friend_hero (友方英雄) - 5维
**描述**: 友方存活英雄数量
**实现逻辑**: 每一位表示一个英雄位置是否存活

### 6. g_enemy_hero (敌方英雄) - 5维
**描述**: 敌方存活英雄数量
**实现逻辑**: 每一位表示一个英雄位置是否存活

### 7. g_hero_diff (英雄差) - 11维
**描述**: 友方存活英雄数 - 敌方存活英雄数
**实现逻辑**: 
- 英雄差范围从-5到+5，映射到11维one-hot编码
- 索引 = 英雄差 + 5

### 8. g_friend_kill (友方击杀) - 3维
**描述**: 友方阵营的总击杀数
**实现逻辑**:
- <5: [1,0,0]
- 5-14: [0,1,0]
- ≥15: [0,0,1]

### 9. g_enemy_kill (敌方击杀) - 3维
**描述**: 敌方阵营的总击杀数
**实现逻辑**: 与友方击杀相同的区间划分

### 10. g_kill_diff (击杀差) - 5维
**描述**: 友方击杀数 - 敌方击杀数
**实现逻辑**:
- <-10 (大幅落后): [1,0,0,0,0]
- -10到-4 (落后): [0,1,0,0,0]
- -3到3 (平衡): [0,0,1,0,0]
- 4到10 (领先): [0,0,0,1,0]
- >10 (大幅领先): [0,0,0,0,1]

### 11. g_friend_organ (友方塔) - 4维
**描述**: 友方存活防御塔数量
**实现逻辑**: 每一位表示一座塔是否存活

### 12. g_enemy_organ (敌方塔) - 4维
**描述**: 敌方存活防御塔数量
**实现逻辑**: 每一位表示一座塔是否存活

### 13. g_organ_diff (塔数差) - 6维
**描述**: 友方塔数 - 敌方塔数
**实现逻辑**:
- <-2: [1,0,0,0,0,0]
- -1: [0,1,0,0,0,0]
- 0: [0,0,1,0,0,0]
- 1: [0,0,0,1,0,0]
- 2: [0,0,0,0,1,0]
- >2: [0,0,0,0,0,1]

### 14. g_frame_no_div_1000 (帧号/1000) - 1维
**描述**: 当前帧号除以1000的整数部分
**实现逻辑**: 直接计算 frameNo // 1000

### 15. g_frame_no_mod_1000 (帧号%1000) - 1维
**描述**: 当前帧号对1000取模的结果
**实现逻辑**: 直接计算 frameNo % 1000

## 代码结构

### 主要文件
1. `agent_ppo/feature/feature_process/global_process.py` - 全局特征处理类
2. `agent_ppo/feature/feature_process/global_feature_config.ini` - 全局特征配置
3. `agent_ppo/feature/feature_process/__init__.py` - 特征处理集成

### 核心类
- `VecCampsWholeInfo`: 全局特征处理主类
- 包含15个特征处理函数，每个函数对应一个全局特征

### 集成方式
全局特征已集成到 `FeatureProcess` 类中，通过 `process_global_feature()` 方法调用。

## 特征维度汇总

| 特征名称 | 维度 | 累计维度 |
|---------|------|----------|
| g_game_time | 5 | 5 |
| g_friend_money | 6 | 11 |
| g_enemy_money | 6 | 17 |
| g_money_diff | 5 | 22 |
| g_friend_hero | 5 | 27 |
| g_enemy_hero | 5 | 32 |
| g_hero_diff | 11 | 43 |
| g_friend_kill | 3 | 46 |
| g_enemy_kill | 3 | 49 |
| g_kill_diff | 5 | 54 |
| g_friend_organ | 4 | 58 |
| g_enemy_organ | 4 | 62 |
| g_organ_diff | 6 | 68 |
| g_frame_no_div_1000 | 1 | 69 |
| g_frame_no_mod_1000 | 1 | 70 |

**总维度**: 70维

## 使用方法

```python
from agent_ppo.feature.feature_process import FeatureProcess

# 创建特征处理器
feature_processor = FeatureProcess("PLAYERCAMP_1")

# 处理特征
observation = {"frame_state": frame_state_data}
features = feature_processor.process_feature(observation)

# 全局特征是features向量的一部分
```

## 注意事项

1. **数据依赖**: 全局特征依赖于frame_state中的hero_states和npc_states数据
2. **金币和击杀数据**: 假设这些数据在hero的actor_state中，可能需要根据实际数据结构调整
3. **归一化**: 所有特征都经过配置文件中定义的归一化处理
4. **阵营转换**: 支持PLAYERCAMP_2到PLAYERCAMP_1的坐标转换
