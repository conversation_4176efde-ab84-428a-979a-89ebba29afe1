observation = {'env_id': '9835444', 'player_id': 32, 'player_camp': 2, 'legal_action': [0.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], 'sub_action_mask': [[1.0, 0.0, 0.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0, 0.0, 0.0], [1.0, 1.0, 1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0, 0.0, 1.0], [1.0, 0.0, 0.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 1.0, 1.0, 1.0], [1.0, 0.0, 0.0, 1.0, 1.0, 1.0], [1.0, 0.0, 0.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 1.0, 1.0, 1.0], [1.0, 0.0, 0.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0, 0.0, 1.0]], 'frame_state': {'frameNo': 614, 'hero_states': [{'player_id': 31, 'actor_state': {'config_id': 169, 'runtime_id': 7, 'actor_type': 'ACTOR_HERO', 'sub_type': 'ACTOR_SUB_NONE', 'camp': 'PLAYERCAMP_1', 'behav_mode': 'State_Idle', 'location': {'x': 100000, 'y': 100000, 'z': 100000}, 'forward': {'x': 100000, 'y': 100000, 'z': 100000}, 'hp': 3412, 'max_hp': 3412, 'values': {'phy_atk': 174, 'phy_def': 176, 'mgc_atk': 0, 'mgc_def': 50, 'mov_spd': 0, 'atk_spd': 0, 'ep': 0, 'max_ep': 440, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 0, 'organ_conti_attack': 0}, 'attack_range': 8000, 'attack_target': 0, 'kill_income': 150, 'camp_visible': [True, False], 'sight_area': 7000, 'buff_state': {'buff_skills': [{'configId': 169100, 'times': 0, 'startTime': '18282'}, {'configId': 169963, 'times': 0, 'startTime': '18513'}, {'configId': 90015, 'times': 0, 'startTime': '20064'}], 'buff_marks': [{'configId': 16901, 'layer': 2, 'origin_actorId': 7}]}}, 'skill_state': {'slot_states': [{'configId': 16903, 'slot_type': 'SLOT_SKILL_0', 'level': 0, 'usable': True, 'cooldown': 0, 'cooldown_max': 825, 'usedTimes': 10, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}, {'configId': 16910, 'slot_type': 'SLOT_SKILL_1', 'level': 0, 'usable': False, 'cooldown': 0, 'cooldown_max': 10000, 'usedTimes': 2, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}, {'configId': 16920, 'slot_type': 'SLOT_SKILL_2', 'level': 0, 'usable': False, 'cooldown': 0, 'cooldown_max': 8000, 'usedTimes': 0, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}, {'configId': 16930, 'slot_type': 'SLOT_SKILL_3', 'level': 0, 'usable': False, 'cooldown': 0, 'cooldown_max': 45000, 'usedTimes': 0, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}, {'configId': 90003, 'slot_type': 'SLOT_SKILL_4', 'level': 0, 'usable': False, 'cooldown': 0, 'cooldown_max': 60000, 'usedTimes': 1, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}, {'configId': 80115, 'slot_type': 'SLOT_SKILL_5', 'level': 0, 'usable': False, 'cooldown': 0, 'cooldown_max': 120000, 'usedTimes': 1, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}, {'configId': 90005, 'slot_type': 'SLOT_SKILL_6', 'level': 0, 'usable': True, 'cooldown': 0, 'cooldown_max': 0, 'usedTimes': 0, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}]}, 'equip_state': {'equips': [{'configId': 1312, 'amount': 1, 'buyPrice': 220}, {'configId': 0, 'amount': 0, 'buyPrice': 0}, {'configId': 0, 'amount': 0, 'buyPrice': 0}, {'configId': 0, 'amount': 0, 'buyPrice': 0}, {'configId': 0, 'amount': 0, 'buyPrice': 0}, {'configId': 0, 'amount': 0, 'buyPrice': 0}]}, 'buff_state': {}, 'level': 1, 'exp': 16, 'money': 112, 'revive_time': 0, 'killCnt': 0, 'deadCnt': 0, 'assistCnt': 0, 'moneyCnt': 332, 'totalHurt': 0, 'totalHurtToHero': 0, 'totalBeHurtByHero': 0, 'passive_skill': [{'passive_skillid': 16901, 'cooldown': 0}, {'passive_skillid': 16902, 'cooldown': 0}], 'isInGrass': False, 'canBuyEquip': False}, {'player_id': 32, 'actor_state': {'config_id': 169, 'runtime_id': 13, 'actor_type': 'ACTOR_HERO', 'sub_type': 'ACTOR_SUB_NONE', 'camp': 'PLAYERCAMP_2', 'behav_mode': 'Normal_Attack', 'location': {'x': 39985, 'y': 48, 'z': 37112}, 'forward': {'x': 720, 'y': 0, 'z': 694}, 'hp': 3412, 'max_hp': 3412, 'values': {'phy_atk': 174, 'phy_def': 176, 'mgc_atk': 0, 'mgc_def': 50, 'mov_spd': 3780, 'atk_spd': 1620, 'ep': 440, 'max_ep': 440, 'hp_recover': 41, 'ep_recover': 16, 'phy_armor_hurt': 64, 'mgc_armor_hurt': 0, 'crit_rate': 560, 'crit_effe': 11080, 'phy_vamp': 800, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 0, 'organ_conti_attack': 0}, 'abilities': [False, True, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False], 'attack_range': 8000, 'attack_target': 0, 'kill_income': 150, 'camp_visible': [False, True], 'sight_area': 7000, 'buff_state': {'buff_skills': [{'configId': 169100, 'times': 0, 'startTime': '16896'}, {'configId': 90015, 'times': 0, 'startTime': '19965'}, {'configId': 169963, 'times': 0, 'startTime': '20097'}], 'buff_marks': [{'configId': 16901, 'layer': 2, 'origin_actorId': 13}]}}, 'skill_state': {'slot_states': [{'configId': 16903, 'slot_type': 'SLOT_SKILL_0', 'level': 1, 'usable': False, 'cooldown': 594, 'cooldown_max': 825, 'usedTimes': 11, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}, {'configId': 16910, 'slot_type': 'SLOT_SKILL_1', 'level': 1, 'usable': False, 'cooldown': 6601, 'cooldown_max': 10000, 'usedTimes': 2, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}, {'configId': 16920, 'slot_type': 'SLOT_SKILL_2', 'level': 0, 'usable': False, 'cooldown': 0, 'cooldown_max': 8000, 'usedTimes': 0, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}, {'configId': 16930, 'slot_type': 'SLOT_SKILL_3', 'level': 0, 'usable': False, 'cooldown': 0, 'cooldown_max': 45000, 'usedTimes': 0, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}, {'configId': 90003, 'slot_type': 'SLOT_SKILL_4', 'level': 1, 'usable': False, 'cooldown': 47691, 'cooldown_max': 60000, 'usedTimes': 1, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}, {'configId': 80115, 'slot_type': 'SLOT_SKILL_5', 'level': 1, 'usable': False, 'cooldown': 104325, 'cooldown_max': 120000, 'usedTimes': 1, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}, {'configId': 90005, 'slot_type': 'SLOT_SKILL_6', 'level': 1, 'usable': False, 'cooldown': 0, 'cooldown_max': 0, 'usedTimes': 0, 'hitHeroTimes': 0, 'succUsedInFrame': 0, 'nextConfigID': 0, 'comboEffectTime': 0}]}, 'equip_state': {'equips': [{'configId': 1312, 'amount': 1, 'buyPrice': 220}, {'configId': 0, 'amount': 0, 'buyPrice': 0}, {'configId': 0, 'amount': 0, 'buyPrice': 0}, {'configId': 0, 'amount': 0, 'buyPrice': 0}, {'configId': 0, 'amount': 0, 'buyPrice': 0}, {'configId': 0, 'amount': 0, 'buyPrice': 0}]}, 'buff_state': {'buff_skills': [{'configId': 169100, 'times': 0, 'startTime': '16896'}, {'configId': 90015, 'times': 0, 'startTime': '19965'}, {'configId': 169963, 'times': 0, 'startTime': '20097'}], 'buff_marks': [{'configId': 16901, 'layer': 2, 'origin_actorId': 13}]}, 'level': 1, 'exp': 16, 'money': 112, 'revive_time': 0, 'killCnt': 0, 'deadCnt': 0, 'assistCnt': 0, 'moneyCnt': 332, 'totalHurt': 0, 'totalHurtToHero': 0, 'totalBeHurtByHero': 0, 'passive_skill': [{'passive_skillid': 16901, 'cooldown': 0}, {'passive_skillid': 16902, 'cooldown': 0}], 'real_cmd': [{'command_type': 'COMMAND_TYPE_AttackCommon', 'move_pos': {'destPos': {'x': 0, 'y': 0, 'z': 0}}, 'move_dir': {'degree': 0}, 'attack_common': {'actorID': 0, 'start': 0}, 'attack_topos': {'destPos': {'x': 0, 'y': 0, 'z': 0}}, 'attack_actor': {'actorID': 0}, 'obj_skill': {'skillID': 0, 'actorID': 0, 'slotType': 'SLOT_SKILL_0'}, 'dir_skill': {'skillID': 0, 'actorID': 0, 'slotType': 'SLOT_SKILL_0', 'degree': 0}, 'pos_skill': {'skillID': 0, 'destPos': {'x': 0, 'y': 0, 'z': 0}, 'slotType': 'SLOT_SKILL_0'}, 'learn_skill': {'slotType': 'SLOT_SKILL_0', 'level': 0}, 'buy_equip': {'equipId': 0, 'obj_id': 0}, 'sell_equip': {'equipIndex': 0}, 'charge_skill': {'slotType': 'SLOT_SKILL_0', 'state': 0, 'degree': 0}}], 'canAbortCurSkill': [False, True, True, True, False, False, False, False, False, False, False, False, False, False, False, False], 'isInGrass': False, 'canBuyEquip': False}], 'npc_states': [{'config_id': 6804, 'runtime_id': 47, 'actor_type': 'ACTOR_MONSTER', 'sub_type': 'ACTOR_SUB_SOLDIER', 'camp': 'PLAYERCAMP_2', 'behav_mode': 'Attack_Path', 'location': {'x': 18783, 'y': 48, 'z': 22273}, 'forward': {'x': -834, 'y': 0, 'z': -552}, 'hp': 1356, 'max_hp': 1356, 'values': {'phy_atk': 90, 'phy_def': 180, 'mgc_atk': 90, 'mgc_def': 0, 'mov_spd': 3088, 'atk_spd': 0, 'ep': 0, 'max_ep': 0, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 750, 'organ_conti_attack': 0}, 'abilities': [False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False], 'attack_range': 7000, 'attack_target': 0, 'kill_income': 46, 'camp_visible': [False, True], 'sight_area': 8500, 'buff_state': {}}, {'config_id': 6804, 'runtime_id': 44, 'actor_type': 'ACTOR_MONSTER', 'sub_type': 'ACTOR_SUB_SOLDIER', 'camp': 'PLAYERCAMP_2', 'behav_mode': 'Attack_Path', 'location': {'x': 16044, 'y': 48, 'z': 20428}, 'forward': {'x': -829, 'y': 0, 'z': -559}, 'hp': 1356, 'max_hp': 1356, 'values': {'phy_atk': 90, 'phy_def': 180, 'mgc_atk': 90, 'mgc_def': 0, 'mov_spd': 3088, 'atk_spd': 0, 'ep': 0, 'max_ep': 0, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 750, 'organ_conti_attack': 0}, 'abilities': [False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False], 'attack_range': 7000, 'attack_target': 0, 'kill_income': 46, 'camp_visible': [False, True], 'sight_area': 8500, 'buff_state': {}}, {'config_id': 6803, 'runtime_id': 40, 'actor_type': 'ACTOR_MONSTER', 'sub_type': 'ACTOR_SUB_SOLDIER', 'camp': 'PLAYERCAMP_2', 'behav_mode': 'Attack_Path', 'location': {'x': 13720, 'y': 48, 'z': 18862}, 'forward': {'x': -829, 'y': 0, 'z': -559}, 'hp': 1488, 'max_hp': 1488, 'values': {'phy_atk': 60, 'phy_def': 180, 'mgc_atk': 60, 'mgc_def': 0, 'mov_spd': 3088, 'atk_spd': 0, 'ep': 0, 'max_ep': 0, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 750, 'organ_conti_attack': 0}, 'abilities': [False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False], 'attack_range': 1500, 'attack_target': 0, 'kill_income': 75, 'camp_visible': [False, True], 'sight_area': 5500, 'buff_state': {}}, {'config_id': 1114, 'runtime_id': 16, 'actor_type': 'ACTOR_ORGAN', 'sub_type': 'ACTOR_SUB_CRYSTAL', 'camp': 'PLAYERCAMP_2', 'behav_mode': 'Attack_Move', 'location': {'x': 31250, 'y': 0, 'z': 31350}, 'forward': {'x': 707, 'y': 0, 'z': -707}, 'hp': 9000, 'max_hp': 9000, 'values': {'phy_atk': 550, 'phy_def': 200, 'mgc_atk': 0, 'mgc_def': 200, 'mov_spd': 5000, 'atk_spd': 0, 'ep': 0, 'max_ep': 0, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 0, 'organ_conti_attack': 0}, 'abilities': [False, True, False, False, False, False, True, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False], 'attack_range': 12000, 'attack_target': 0, 'kill_income': 0, 'camp_visible': [True, True], 'sight_area': 9300, 'buff_state': {}}, {'config_id': 1112, 'runtime_id': 15, 'actor_type': 'ACTOR_ORGAN', 'sub_type': 'ACTOR_SUB_TOWER', 'camp': 'PLAYERCAMP_2', 'behav_mode': 'Attack_Move', 'location': {'x': 13131, 'y': 0, 'z': 13258}, 'forward': {'x': 707, 'y': 0, 'z': -707}, 'hp': 12000, 'max_hp': 12000, 'values': {'phy_atk': 470, 'phy_def': 200, 'mgc_atk': 0, 'mgc_def': 200, 'mov_spd': 5000, 'atk_spd': 0, 'ep': 0, 'max_ep': 0, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 0, 'organ_conti_attack': 0}, 'abilities': [False, True, False, False, False, False, True, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False], 'attack_range': 8800, 'attack_target': 0, 'kill_income': 50, 'camp_visible': [True, True], 'sight_area': 8800, 'buff_state': {}}, {'config_id': 1111, 'runtime_id': 14, 'actor_type': 'ACTOR_ORGAN', 'sub_type': 'ACTOR_SUB_TOWER', 'camp': 'PLAYERCAMP_1', 'behav_mode': 'Attack_Move', 'location': {'x': -13138, 'y': 0, 'z': -12940}, 'forward': {'x': -707, 'y': 0, 'z': 707}, 'hp': 12000, 'max_hp': 12000, 'values': {'phy_atk': 470, 'phy_def': 200, 'mgc_atk': 0, 'mgc_def': 200, 'mov_spd': 5000, 'atk_spd': 0, 'ep': 0, 'max_ep': 0, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 0, 'organ_conti_attack': 0}, 'abilities': [False, True, False, False, False, False, True, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False], 'attack_range': 8800, 'attack_target': 0, 'kill_income': 50, 'camp_visible': [True, True], 'sight_area': 8800, 'buff_state': {}}, {'config_id': 1113, 'runtime_id': 9, 'actor_type': 'ACTOR_ORGAN', 'sub_type': 'ACTOR_SUB_CRYSTAL', 'camp': 'PLAYERCAMP_1', 'behav_mode': 'Attack_Move', 'location': {'x': -31310, 'y': 50, 'z': -31120}, 'forward': {'x': 0, 'y': 0, 'z': 1000}, 'hp': 9000, 'max_hp': 9000, 'values': {'phy_atk': 550, 'phy_def': 200, 'mgc_atk': 0, 'mgc_def': 200, 'mov_spd': 5000, 'atk_spd': 0, 'ep': 0, 'max_ep': 0, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 0, 'organ_conti_attack': 0}, 'abilities': [False, True, False, False, False, False, True, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False], 'attack_range': 12000, 'attack_target': 0, 'kill_income': 0, 'camp_visible': [True, True], 'sight_area': 9300, 'buff_state': {}}, {'config_id': 46, 'runtime_id': 8, 'actor_type': 'ACTOR_ORGAN', 'sub_type': 'ACTOR_SUB_TOWER_SPRING', 'camp': 'PLAYERCAMP_2', 'behav_mode': 'Attack_Move', 'location': {'x': 45785, 'y': 1120, 'z': 45856}, 'forward': {'x': 0, 'y': 0, 'z': -1000}, 'hp': 6000, 'max_hp': 6000, 'values': {'phy_atk': 9999, 'phy_def': 0, 'mgc_atk': 0, 'mgc_def': 0, 'mov_spd': 5000, 'atk_spd': 0, 'ep': 0, 'max_ep': 0, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 0, 'organ_conti_attack': 0}, 'abilities': [False, True, False, False, False, False, True, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False], 'attack_range': 13000, 'attack_target': 0, 'kill_income': 150, 'camp_visible': [True, True], 'sight_area': 8000, 'buff_state': {}}, {'config_id': 44, 'runtime_id': 2, 'actor_type': 'ACTOR_ORGAN', 'sub_type': 'ACTOR_SUB_TOWER_SPRING', 'camp': 'PLAYERCAMP_1', 'behav_mode': 'Attack_Move', 'location': {'x': -45396, 'y': 1120, 'z': -45467}, 'forward': {'x': 0, 'y': 0, 'z': 1000}, 'hp': 6000, 'max_hp': 6000, 'values': {'phy_atk': 9999, 'phy_def': 0, 'mgc_atk': 0, 'mgc_def': 0, 'mov_spd': 5000, 'atk_spd': 0, 'ep': 0, 'max_ep': 0, 'hp_recover': 0, 'ep_recover': 0, 'phy_armor_hurt': 0, 'mgc_armor_hurt': 0, 'crit_rate': 0, 'crit_effe': 0, 'phy_vamp': 0, 'mgc_vamp': 0, 'cd_reduce': 0, 'ctrl_reduce': 0, 'monster_endurance': 0, 'organ_conti_attack': 0}, 'abilities': [False, True, False, False, False, False, True, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False, False], 'attack_range': 13000, 'attack_target': 0, 'kill_income': 150, 'camp_visible': [True, True], 'sight_area': 8000, 'buff_state': {}}], 'bullets': [{'runtime_id': 55, 'camp': 'PLAYERCAMP_2', 'source_actor': 13, 'slot_type': 'SLOT_SKILL_0', 'skill_id': 0, 'location': {'x': 41417, 'y': 1548, 'z': 38491}}], 'frame_action': {}, 'map_state': True}, 'win': 0, 'reward': {'tower_hp_point': 0.0, 'forward': -0.9673432723581313, 'reward_sum': -0.009673432723581314}}
Feature values: [1, 0.16679166666666667, 0.19073333333333334, 1, 0, 0, 0.3412, 1, 0.3412, 0.01201641266119578, 1, 0, 0.660219512195122, 0.6578048780487805, 1, 1, 1]